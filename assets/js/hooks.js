// LiveView Hooks Module
// This module contains all the LiveView hooks extracted from app.js

import Sortable from "sortablejs"
import { gsap } from "gsap"
import { SlickGrid, SlickDataView, SlickGroupItemMetadataProvider } from "slickgrid"
import { PayloadEditor, UnifiedPayloadEditor } from "./payload_editor"
import FileUpload from "./file_upload_hook"
import { TraceSlickGrid } from "./trace-slick-grid-hook"

// Hook for handling sortable broker tabs
const BrokerTabsSortable = {
  mounted() {
    const container = this.el;

    // Initialize Sortable for broker tabs
    this.sortable = new Sortable(container, {
      animation: 150,
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',

      // Only allow dragging of broker tabs, not buttons or close buttons
      filter: 'button, .add-broker-btn',
      preventOnFilter: true,

      // Prevent dragging when clicking on close buttons
      onStart: (evt) => {
        // Check if the click target is a close button or its child
        const target = evt.originalEvent.target;
        const closeButton = target.closest('span[phx-click="close_broker_tab"]');
        if (closeButton) {
          // Cancel the drag operation
          return false;
        }
      },

      // Event triggered when sorting is stopped
      onEnd: (evt) => {
        const oldIndex = evt.oldIndex;
        const newIndex = evt.newIndex;

        // Only push the event if the order actually changed
        if (oldIndex !== newIndex) {
          // Send the reordering event to the server
          this.pushEvent('reorder_broker_tabs', {
            old_index: oldIndex,
            new_index: newIndex
          });
        }
      }
    });

    // Handle close button clicks manually to ensure they work
    this.handleCloseButtonClick = (event) => {
      const closeButton = event.target.closest('span[phx-click="show_delete_confirm"]');
      if (closeButton) {
        event.stopPropagation();
        event.preventDefault();

        // Get the broker name from the phx-value-name attribute
        const brokerName = closeButton.getAttribute('phx-value-name');
        const target = closeButton.getAttribute('phx-target');

        if (brokerName && target) {
          // Send the show delete confirm event to the specific component target
          this.pushEventTo(target, 'show_delete_confirm', { name: brokerName });
        }
      }
    };

    // Add click event listener to the container for close buttons
    container.addEventListener('click', this.handleCloseButtonClick);
  },

  destroyed() {
    // Clean up event listeners
    if (this.handleCloseButtonClick) {
      this.el.removeEventListener('click', this.handleCloseButtonClick);
    }

    // Clean up Sortable instance when the element is removed
    if (this.sortable) {
      this.sortable.destroy();
    }
  }
};

// Hook for handling protocol changes in connection set forms
const ProtocolSelector = {
  mounted() {
    this.handleProtocolChange = () => {
      const protocol = this.el.value;
      const sslContainer = document.getElementById('ssl-section-container');
      const websocketPathContainer = document.getElementById('websocket-path-section');
      const portInput = document.querySelector('input[name="connection_set[port]"]');
      const wsPathInput = document.querySelector('input[name="connection_set[ws_path]"]');

      // Show SSL section for secure protocols
      if (sslContainer) {
        if (['mqtts', 'wss', 'quic'].includes(protocol)) {
          sslContainer.style.display = 'block';
        } else {
          sslContainer.style.display = 'none';
        }
      }

      // Show WebSocket path section for WebSocket protocols
      if (websocketPathContainer) {
        if (['ws', 'wss'].includes(protocol)) {
          websocketPathContainer.style.display = 'block';
          // Set default WebSocket path if empty
          if (wsPathInput && !wsPathInput.value) {
            wsPathInput.value = '/mqtt';
          }
        } else {
          websocketPathContainer.style.display = 'none';
        }
      }

      // Update port field based on protocol
      if (portInput) {
        // Only update port if it's a default port value
        const defaultPorts = {
          mqtt: "1883",
          mqtts: "8883",
          ws: "8083",
          wss: "8084",
          quic: "14567"
        };

        // Check if current port is one of the default ports
        const isDefaultPort = Object.values(defaultPorts).includes(portInput.value);

        // Only update if it's a default port to avoid overwriting custom ports
        if (isDefaultPort) {
          portInput.value = defaultPorts[protocol] || "1883";
        }
      }
    };

    // Set initial state
    this.handleProtocolChange();

    // Add event listener for changes
    this.el.addEventListener('change', this.handleProtocolChange);
  },

  destroyed() {
    // Clean up event listener
    if (this.handleProtocolChange) {
      this.el.removeEventListener('change', this.handleProtocolChange);
    }
  }
};

// Hook for handling connection table animations with GSAP
const ConnectionTableAutoAnimate = {
  mounted() {
    this.setupGSAPAnimations();
    this.observeTableChanges();
  },

  updated() {
    // Detect reordering and animate accordingly
    this.handleReordering();
    // Re-observe table changes when component updates
    this.observeTableChanges();
  },

  setupGSAPAnimations() {
    // Set up GSAP defaults for smooth animations
    gsap.defaults({
      duration: 0.4,
      ease: "power2.out"
    });

    // Store reference to table body for animations
    this.tableBody = this.el.querySelector('tbody');
    this.previousRows = new Map();
    this.isReordering = false;

    if (this.tableBody) {
      // Store initial state of rows
      this.captureRowState();
    }
  },

  observeTableChanges() {
    if (!this.tableBody) return;

    // Create a mutation observer to detect row changes
    if (this.observer) {
      this.observer.disconnect();
    }

    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          this.handleRowChanges(mutation);
        }
      });
    });

    this.observer.observe(this.tableBody, {
      childList: true,
      subtree: true
    });

    // Also observe status changes within existing rows
    if (this.statusObserver) {
      this.statusObserver.disconnect();
    }

    this.statusObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          this.handleStatusChange(mutation.target);
        }
      });
    });

    // Observe all status elements for changes
    const statusElements = this.tableBody.querySelectorAll('.swap input[type="checkbox"]');
    statusElements.forEach(element => {
      this.statusObserver.observe(element, {
        attributes: true,
        attributeFilter: ['checked']
      });
    });
  },

  handleReordering() {
    if (!this.tableBody || this.isReordering) return;

    const currentRows = this.tableBody.querySelectorAll('tr');
    const currentOrder = Array.from(currentRows).map(row => this.getRowClientId(row)).filter(Boolean);
    const previousOrder = Array.from(this.previousRows.keys());

    // Check if this is a reordering (same items, different order)
    if (this.isReorderingDetected(currentOrder, previousOrder)) {
      this.animateReordering(currentRows);
    }

    // Update state after potential reordering
    setTimeout(() => this.captureRowState(), 50);
  },

  isReorderingDetected(currentOrder, previousOrder) {
    // Check if we have the same items but in different order
    if (currentOrder.length !== previousOrder.length) return false;

    const currentSet = new Set(currentOrder);
    const previousSet = new Set(previousOrder);

    // Same items but different order
    return currentSet.size === previousSet.size &&
           [...currentSet].every(item => previousSet.has(item)) &&
           !currentOrder.every((item, index) => item === previousOrder[index]);
  },

  animateReordering(currentRows) {
    this.isReordering = true;

    // FLIP animation technique for smooth reordering
    const animations = [];

    currentRows.forEach((row) => {
      const clientId = this.getRowClientId(row);
      if (!clientId || !this.previousRows.has(clientId)) return;

      const previousData = this.previousRows.get(clientId);
      const currentRect = row.getBoundingClientRect();
      const previousRect = previousData.rect;

      // Calculate the difference (FLIP: First, Last, Invert, Play)
      const deltaY = previousRect.top - currentRect.top;
      const deltaX = previousRect.left - currentRect.left;

      if (Math.abs(deltaY) > 1 || Math.abs(deltaX) > 1) {
        // Set initial position (Invert)
        gsap.set(row, {
          y: deltaY,
          x: deltaX
        });

        // Animate to final position (Play)
        const animation = gsap.to(row, {
          duration: 0.6,
          y: 0,
          x: 0,
          ease: "power2.out",
          onComplete: () => {
            // Clear transforms
            gsap.set(row, { clearProps: "all" });
          }
        });

        animations.push(animation);
      }
    });

    // Add stagger effect for visual appeal
    if (animations.length > 0) {
      gsap.to(currentRows, {
        duration: 0.1,
        scale: 1.02,
        yoyo: true,
        repeat: 1,
        stagger: 0.03,
        ease: "power2.inOut",
        onComplete: () => {
          this.isReordering = false;
        }
      });
    } else {
      this.isReordering = false;
    }
  },

  captureRowState() {
    if (!this.tableBody) return;

    const rows = this.tableBody.querySelectorAll('tr');
    this.previousRows.clear();

    rows.forEach((row, index) => {
      const clientId = this.getRowClientId(row);
      if (clientId) {
        this.previousRows.set(clientId, {
          element: row,
          index: index,
          rect: row.getBoundingClientRect()
        });
      }
    });
  },

  getRowClientId(row) {
    // Extract client ID from the row's data or content
    const clientIdCell = row.querySelector('td:nth-child(2) span');
    return clientIdCell ? clientIdCell.textContent.trim() : null;
  },

  handleRowChanges(mutation) {
    // Skip animations if we're currently reordering
    if (this.isReordering) return;

    // Handle removed nodes (connections disappearing)
    mutation.removedNodes.forEach((node) => {
      if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
        this.animateRowRemoval(node);
      }
    });

    // Handle added nodes (connections appearing)
    mutation.addedNodes.forEach((node) => {
      if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
        this.animateRowAddition(node);
      }
    });

    // Update row state after changes
    setTimeout(() => this.captureRowState(), 50);
  },

  handleStatusChange(element) {
    // Find the row containing this status element
    const row = element.closest('tr');
    if (!row) return;

    // Add a visual indicator that the status is changing
    row.classList.add('status-changing');

    // Add a subtle pulse animation to indicate the change
    gsap.fromTo(row,
      {
        backgroundColor: 'rgba(59, 130, 246, 0.15)',
        scale: 1
      },
      {
        duration: 0.6,
        backgroundColor: 'transparent',
        scale: 1.01,
        ease: "power2.out",
        yoyo: true,
        repeat: 1,
        onComplete: () => {
          row.classList.remove('status-changing');
        }
      }
    );

    // The row removal will be handled by the main mutation observer
    // when LiveView updates the DOM
  },

  animateRowRemoval(row) {
    // Create a clone of the row for animation
    const clone = row.cloneNode(true);
    clone.style.position = 'absolute';
    clone.style.left = '0';
    clone.style.right = '0';
    clone.style.zIndex = '10';
    clone.style.pointerEvents = 'none';

    // Insert clone at the same position
    if (row.parentNode) {
      row.parentNode.insertBefore(clone, row.nextSibling);
    }

    // Animate the clone out
    gsap.to(clone, {
      duration: 0.5,
      x: -50,
      opacity: 0,
      scale: 0.95,
      ease: "power2.in",
      onComplete: () => {
        if (clone.parentNode) {
          clone.parentNode.removeChild(clone);
        }
      }
    });

    // Animate remaining rows moving up
    this.animateRemainingRows();
  },

  animateRowAddition(row) {
    // Skip if we're reordering (this might be a repositioned row)
    if (this.isReordering) return;

    // Set initial state for new row with more subtle entrance
    gsap.set(row, {
      y: -20,
      opacity: 0,
      scale: 0.95
    });

    // Animate row in with smooth entrance
    gsap.to(row, {
      duration: 0.5,
      y: 0,
      opacity: 1,
      scale: 1,
      ease: "power2.out",
      delay: 0.1
    });
  },

  animateRemainingRows() {
    if (!this.tableBody) return;

    const currentRows = this.tableBody.querySelectorAll('tr');

    currentRows.forEach((row, index) => {
      // Add subtle animation to show the table is updating
      gsap.fromTo(row,
        {
          y: -5,
          opacity: 0.8
        },
        {
          duration: 0.3,
          y: 0,
          opacity: 1,
          ease: "power2.out",
          delay: index * 0.02 // Stagger effect
        }
      );
    });
  },

  destroyed() {
    // Clean up observers
    if (this.observer) {
      this.observer.disconnect();
    }

    if (this.statusObserver) {
      this.statusObserver.disconnect();
    }

    // Kill any running GSAP animations
    if (this.tableBody) {
      gsap.killTweensOf(this.tableBody.querySelectorAll('tr'));
    }
  }
};

// Hook for handling trace table animations with GSAP - inspired by infinite_scroll_packets.html
const TraceTableGSAP = {
  mounted() {
    this.setupGSAPAnimations();
    this.observeTableChanges();
    this.setupSmoothScrolling();
  },

  updated() {
    // Re-observe table changes when component updates
    this.observeTableChanges();
  },

  setupGSAPAnimations() {
    // Set up GSAP defaults for smooth, natural animations matching reference
    gsap.defaults({
      duration: 0.6,
      ease: "back.out(1.7)"
    });

    // Store reference to table body and wrapper for animations
    this.tableBody = this.el;
    this.tableWrapper = this.el.closest('.trace-message-table-wrapper');
    this.previousRows = new Map();

    if (this.tableBody) {
      // Store initial state of rows
      this.captureRowState();
    }
  },

  setupSmoothScrolling() {
    if (this.tableWrapper) {
      // Enable smooth scrolling with GSAP
      this.tableWrapper.style.scrollBehavior = 'auto'; // Disable CSS smooth scroll

      // Store scroll position for auto-scroll functionality
      this.isAtTop = true;
      this.userScrolled = false;

      // Listen for scroll events to detect user interaction
      this.tableWrapper.addEventListener('scroll', () => {
        const { scrollTop } = this.tableWrapper;
        this.isAtTop = scrollTop <= 10;
        this.userScrolled = true;

        // Reset user scroll flag after a delay
        clearTimeout(this.scrollTimeout);
        this.scrollTimeout = setTimeout(() => {
          this.userScrolled = false;
        }, 1000);
      });
    }
  },

  observeTableChanges() {
    if (this.observer) {
      this.observer.disconnect();
    }

    if (!this.tableBody) return;

    // Create mutation observer to watch for DOM changes
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        this.handleRowChanges(mutation);
      });
    });

    // Start observing
    this.observer.observe(this.tableBody, {
      childList: true,
      subtree: false
    });
  },

  captureRowState() {
    if (!this.tableBody) return;

    const rows = this.tableBody.querySelectorAll('tr');
    this.previousRows.clear();

    rows.forEach((row) => {
      if (row.id) {
        this.previousRows.set(row.id, {
          element: row,
          rect: row.getBoundingClientRect()
        });
      }
    });
  },

  handleRowChanges(mutation) {
    // Handle added nodes (new messages) - inspired by infinite_scroll_packets.html
    mutation.addedNodes.forEach((node) => {
      if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
        // Check if this is a new message (has trace-message-new class)
        const isNewMessage = node.classList.contains('trace-message-new');
        this.animateRowAddition(node, isNewMessage);
      }
    });

    // Handle removed nodes (messages being removed)
    mutation.removedNodes.forEach((node) => {
      if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
        this.animateRowRemoval(node);
      }
    });

    // Update row state after changes
    setTimeout(() => this.captureRowState(), 100);

    // Auto-scroll to top if user hasn't manually scrolled and we're viewing latest messages
    if (!this.userScrolled && this.isAtTop) {
      this.smoothScrollToTop();
    }
  },

  animateRowAddition(row, isNewMessage = true) {
    if (isNewMessage) {
      // Add CSS class for new row initial state
      row.classList.add('trace-row-new');

      // Set initial state for new row - matching infinite_scroll_packets.html style
      gsap.set(row, {
        opacity: 0,
        y: -30,
        scale: 0.95
      });

      // Add new message highlight effect
      row.classList.add('new-packet');

      // Set initial green highlight background
      gsap.set(row, {
        background: 'linear-gradient(90deg, #4caf50, transparent)'
      });

      // Animate row in with back.out easing like the reference
      gsap.to(row, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.6,
        ease: "back.out(1.7)",
        onComplete: () => {
          row.classList.remove('trace-row-new');
        }
      });

      // Animate the highlight background fade
      gsap.to(row, {
        background: 'transparent',
        duration: 2,
        ease: "power2.out",
        delay: 0.5,
        onComplete: () => {
          row.classList.remove('new-packet');
        }
      });

      // Push down other rows effect
      this.animateOtherRows(row);

      // Auto-scroll to top to show the new message
      setTimeout(() => {
        this.smoothScrollToTop();
      }, 100);
    } else {
      // For non-new messages (initial load), they are already visible by default
      // Just add a subtle fade-in effect
      gsap.fromTo(row,
        { opacity: 0 },
        {
          opacity: 1,
          duration: 0.5,
          ease: "power2.out",
          delay: Math.random() * 0.3
        }
      );
    }
  },

  animateOtherRows(newRow) {
    // Push down effect for other rows when new message arrives
    const otherRows = Array.from(this.tableBody.children).filter(row => row !== newRow);

    gsap.to(otherRows, {
      y: 5,
      duration: 0.3,
      stagger: 0.02,
      yoyo: true,
      repeat: 1,
      ease: "power2.out"
    });
  },

  animateRowRemoval(row) {
    // Simple fade out for removed rows
    gsap.to(row, {
      duration: 0.4,
      opacity: 0,
      x: -30,
      scale: 0.95,
      ease: "power2.in"
    });
  },

  smoothScrollToTop() {
    if (!this.tableWrapper) return;

    // Use GSAP for smooth scrolling to top (newest messages)
    gsap.to(this.tableWrapper, {
      duration: 0.8,
      scrollTo: { y: 0 },
      ease: "power2.out"
    });
  },

  destroyed() {
    // Clean up observers and timers
    if (this.observer) {
      this.observer.disconnect();
    }

    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }

    // Kill any running GSAP animations
    if (this.tableBody) {
      gsap.killTweensOf(this.tableBody.querySelectorAll('tr'));
    }

    if (this.tableWrapper) {
      gsap.killTweensOf(this.tableWrapper);
    }
  }
};

// Hook for handling trace row highlighting
const TraceRowHighlight = {
  mounted() {
    this.setupRowHighlighting();
  },

  updated() {
    this.setupRowHighlighting();
  },

  setupRowHighlighting() {
    const container = this.el;
    const tableBody = container.querySelector('#trace-message-table-body');
    if (!tableBody) return;

    // Remove existing event listeners to avoid duplicates
    if (this.handleRowClick) {
      tableBody.removeEventListener('click', this.handleRowClick);
    }

    // Add click event listener to the table body
    this.handleRowClick = (event) => {
      const clickedRow = event.target.closest('tr');
      if (!clickedRow || clickedRow.id === 'loading-more-row') return;

      // Remove highlight from all rows
      const allRows = tableBody.querySelectorAll('tr.trace-message-row');
      allRows.forEach(row => {
        row.classList.remove('js-highlighted');
      });

      // Add highlight to clicked row with animation
      clickedRow.classList.add('js-highlighted');

      // Add a subtle pulse effect
      clickedRow.style.animation = 'none';
      // Force reflow
      clickedRow.offsetHeight;
      clickedRow.style.animation = 'row-highlight-pulse 0.4s ease-out';

      // Remove the animation after it completes
      setTimeout(() => {
        if (clickedRow.style) {
          clickedRow.style.animation = '';
        }
      }, 400);
    };

    tableBody.addEventListener('click', this.handleRowClick);
    // Store reference for cleanup
    this.tableBody = tableBody;
  },

  destroyed() {
    if (this.tableBody && this.handleRowClick) {
      this.tableBody.removeEventListener('click', this.handleRowClick);
    }
  }
};

// Hook for JSON Viewer integration
const JsonViewer = {
  mounted() {
    this.initializeJsonViewer();
  },

  updated() {
    this.initializeJsonViewer();
  },

  initializeJsonViewer() {
    const container = this.el;
    const jsonData = container.dataset.json;

    if (!jsonData) {
      container.innerHTML = '<div class="json-viewer-error">No JSON data provided</div>';
      return;
    }

    try {
      // Parse the JSON data
      const parsedData = JSON.parse(jsonData);

      // Clear container
      container.innerHTML = '';

      // Create json-viewer element
      const viewer = document.createElement('json-viewer');
      viewer.data = parsedData;
      viewer.expanded = 2; // Expand 2 levels by default

      // Add custom styling classes
      viewer.classList.add('geeky-json-viewer');

      container.appendChild(viewer);
    } catch (error) {
      // If JSON parsing fails, show the raw data with error message
      container.innerHTML = `
        <div class="json-viewer-error">
          <div class="json-error-header">Invalid JSON Data</div>
          <pre class="json-raw-content">${jsonData}</pre>
        </div>
      `;
    }
  },

  destroyed() {
    // Clean up if needed
    if (this.el) {
      this.el.innerHTML = '';
    }
  }
};

// Hook for global keyboard shortcuts
const GlobalKeyboardShortcuts = {
  mounted() {
    // Handle global keyboard shortcuts
    this.handleKeyDown = (event) => {
      // Check if Ctrl+K (Windows/Linux) or Cmd+K (Mac) is pressed
      const isShortcutPressed = (event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'k';

      if (!isShortcutPressed) return;

      // Don't trigger if user is typing in an input field
      const activeElement = document.activeElement;
      const isInputElement = activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.tagName === 'SELECT' ||
        activeElement.contentEditable === 'true'
      );

      if (isInputElement) return;

      // Prevent default behavior
      event.preventDefault();
      event.stopPropagation();

      // Check if the send message modal is currently open
      const sendModal = document.getElementById('send-message-modal');
      const isModalOpen = sendModal && sendModal.classList.contains('modal-open');

      if (isModalOpen) {
        // Modal is open, close it
        this.pushEvent('close_send_modal', {});
      } else {
        // Modal is closed, open it
        this.pushEvent('open_send_modal', {});
      }
    };

    // Add global event listener
    document.addEventListener('keydown', this.handleKeyDown);
  },

  destroyed() {
    // Clean up event listener
    if (this.handleKeyDown) {
      document.removeEventListener('keydown', this.handleKeyDown);
    }
  }
};

// Hook for copying text to clipboard
const CopyToClipboard = {
  mounted() {
    // Handle copy_to_clipboard events from LiveView
    this.handleEvent("copy_to_clipboard", ({ text }) => {
      this.copyToClipboard(text);
    });
  },

  copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
      // Use modern clipboard API if available
      navigator.clipboard.writeText(text).then(() => {
        this.showCopyFeedback(true);
      }).catch((err) => {
        console.error('Failed to copy text: ', err);
        this.fallbackCopyTextToClipboard(text);
      });
    } else {
      // Fallback for older browsers or non-secure contexts
      this.fallbackCopyTextToClipboard(text);
    }
  },

  fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const successful = document.execCommand('copy');
      this.showCopyFeedback(successful);
    } catch (err) {
      console.error('Fallback: Oops, unable to copy', err);
      this.showCopyFeedback(false);
    }

    document.body.removeChild(textArea);
  },

  showCopyFeedback(success) {
    // Create a temporary toast notification
    const toast = document.createElement('div');
    toast.className = `alert ${success ? 'alert-success' : 'alert-error'} fixed top-4 right-4 z-50 w-auto max-w-sm shadow-lg`;
    toast.innerHTML = `
      <div class="flex items-center gap-2">
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          ${success
            ? '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>'
            : '<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>'
          }
        </svg>
        <span class="text-sm">${success ? 'Payload copied to clipboard!' : 'Failed to copy payload'}</span>
      </div>
    `;

    document.body.appendChild(toast);

    // Remove toast after 3 seconds
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 3000);
  }
};

// Export all hooks
export {
  BrokerTabsSortable,
  ProtocolSelector,
  ConnectionTableAutoAnimate,
  TraceTableGSAP,
  TraceRowHighlight,
  JsonViewer,
  TraceSlickGrid,
  PayloadEditor,
  UnifiedPayloadEditor,
  FileUpload,
  GlobalKeyboardShortcuts,
  CopyToClipboard
};
