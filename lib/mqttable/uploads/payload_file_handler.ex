defmodule Mqttable.Uploads.PayloadFileHandler do
  @moduledoc """
  Module for handling payload file uploads.
  Provides centralized functionality for processing and managing payload files.
  """
  require Logger

  # Payload files directory constants
  @files_dir "priv/data/files"

  # Upload options for payload files
  @upload_options [
    accept: :any,
    max_entries: 1,
    # 16MB maximum file size for payload files
    max_file_size: 16_000_000
  ]

  @doc """
  Returns the standard upload options for payload files.
  """
  def upload_options, do: @upload_options

  @doc """
  Returns the path to the files directory.
  """
  def files_dir, do: @files_dir

  @doc """
  Ensures the files directory exists.
  """
  def ensure_files_dir do
    files_dir = Path.join([:code.priv_dir(:mqttable), "data", "files"])
    File.mkdir_p!(files_dir)
    files_dir
  end

  @doc """
  Processes an uploaded payload file.

  ## Parameters
  - `socket`: The LiveView socket
  - `upload_name`: The name of the upload field (e.g., :payload_file)
  - `existing_path`: The existing file path (if any)

  ## Returns
  - `{path, socket}`: The path to the uploaded file and the updated socket
  """
  def process_uploaded_file(socket, upload_name, existing_path) do
    # Ensure the files directory exists
    files_dir = ensure_files_dir()

    # Check if there are any uploaded entries for this upload name
    uploaded_files =
      Phoenix.LiveView.Upload.consume_uploaded_entries(socket, upload_name, fn %{path: path},
                                                                               entry ->
        # Generate a unique filename with the original file extension
        original_filename = entry.client_name
        # Extract the file extension
        ext = Path.extname(original_filename)
        # Generate a unique filename with timestamp
        now = DateTime.utc_now()

        formatted_timestamp =
          "#{now.year}#{pad_number(now.month)}#{pad_number(now.day)}#{pad_number(now.hour)}#{pad_number(now.minute)}#{pad_number(now.second)}_#{pad_number(now.microsecond |> elem(0), 6)}"

        # Remove "file" from the upload_name (e.g., payload_file -> payload)
        clean_name = Atom.to_string(upload_name) |> String.replace("_file", "")
        unique_name = "#{clean_name}_#{formatted_timestamp}#{ext}"

        # Create destination path in files directory
        dest = Path.join(files_dir, unique_name)

        # Copy the file to the destination
        File.cp!(path, dest)

        # Return the path relative to the files directory
        {:ok, "/files/#{unique_name}"}
      end)

    # If there are uploaded files, use the first one, otherwise keep the existing path
    case uploaded_files do
      [path | _] -> {path, socket}
      [] -> {existing_path, socket}
    end
  end

  @doc """
  Reads the content of a payload file from its path.

  ## Parameters
  - `file_path`: The file path (e.g., "/files/payload_20250719123456_123456.txt")

  ## Returns
  - `{:ok, content}`: The file content as binary
  - `{:error, reason}`: Error if file cannot be read
  """
  def read_file_content(file_path) when is_binary(file_path) and file_path != "" do
    # Extract filename from path (remove /files/ prefix)
    filename = String.replace_prefix(file_path, "/files/", "")
    
    # Build full path to file
    files_dir = Path.join([:code.priv_dir(:mqttable), "data", "files"])
    full_path = Path.join(files_dir, filename)

    case File.read(full_path) do
      {:ok, content} -> {:ok, content}
      {:error, reason} -> 
        Logger.warning("Failed to read payload file: #{full_path}, reason: #{reason}")
        {:error, reason}
    end
  end

  def read_file_content(_), do: {:error, :invalid_path}

  @doc """
  Checks if a file path is an image file based on its extension.
  """
  def is_image_file?(file_path) when is_binary(file_path) do
    ext = Path.extname(file_path) |> String.downcase()
    ext in [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"]
  end

  def is_image_file?(_), do: false

  @doc """
  Cleans up unused payload files in the files directory.

  ## Parameters
  - `referenced_files`: List of file paths that are currently referenced
  - `max_age_hours`: Maximum age in hours for unused files before deletion (default: 24)
  """
  def cleanup_unused_files(referenced_files \\ [], max_age_hours \\ 24) do
    # Get the files directory path
    files_dir = Path.join([:code.priv_dir(:mqttable), "data", "files"])

    # Ensure the directory exists
    unless File.exists?(files_dir) do
      # Skip cleanup if directory doesn't exist
      :ok
    else
      # Extract filenames from referenced file paths
      referenced_filenames =
        referenced_files
        |> Enum.filter(&(is_binary(&1) && &1 != ""))
        |> Enum.map(&String.replace_prefix(&1, "/files/", ""))
        |> Enum.uniq()

      # Get all files in the files directory
      case File.ls(files_dir) do
        {:ok, all_files} ->
          # Check which files are not referenced
          unused_files = all_files -- referenced_filenames

          if length(unused_files) > 0 do
            # Calculate age threshold
            current_time = System.system_time(:second)
            age_threshold_seconds = max_age_hours * 3600

            # Check each unused file
            Enum.each(unused_files, fn file ->
              file_path = Path.join(files_dir, file)

              case File.stat(file_path, time: :posix) do
                {:ok, stat} ->
                  creation_time = stat.ctime
                  age_in_seconds = current_time - creation_time

                  # Delete files older than the threshold
                  if age_in_seconds > age_threshold_seconds do
                    case File.rm(file_path) do
                      :ok ->
                        Logger.info(
                          "Deleted unused payload file: #{file} (age: #{age_in_seconds / 3600} hours)"
                        )

                      {:error, _reason} ->
                        # Just skip files we can't delete
                        :ok
                    end
                  end

                {:error, _reason} ->
                  # Skip files we can't stat
                  :ok
              end
            end)
          end

        {:error, reason} ->
          Logger.error("Error listing files in payload files directory: #{reason}")
      end
    end
  end

  # Helper function to pad numbers with leading zeros
  defp pad_number(number, width \\ 2) do
    number
    |> Integer.to_string()
    |> String.pad_leading(width, "0")
  end
end
