defmodule MqttableWeb.MessageDetailModalComponent do
  @moduledoc """
  A reusable modal component for displaying MQTT message details.

  This component provides a detailed view of MQTT messages with support for:
  - Message details display with packet-specific information
  - Payload viewing in different formats (plaintext, JSON, hex)
  - Click-outside-to-close functionality
  - Right-aligned positioning consistent with send message modal
  """

  use MqttableWeb, :live_component

  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:show_modal, fn -> false end)
      |> assign_new(:message, fn -> nil end)
      |> assign_new(:payload_view_type, fn -> "plaintext" end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <!-- Message Detail Modal -->
      <dialog id="message-detail-modal" class={"modal #{if @show_modal, do: "modal-open", else: ""}"}>
        <div class="modal-backdrop" phx-click="close_detail_modal"></div>
        <div class="modal-box max-w-2xl ml-auto mr-6 mt-6 mb-6 h-fit max-h-[calc(100vh-3rem)] message-detail-modal">
          <!-- Modal Header -->
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold flex items-center">
              <.icon name="hero-document-text" class="size-5 mr-2" /> Message Details
            </h3>
            <button class="btn btn-sm btn-circle btn-ghost" phx-click="close_detail_modal">
              ✕
            </button>
          </div>
          
    <!-- Modal Content -->
          <div class="space-y-4 overflow-y-auto max-h-[calc(100vh-8rem)]">
            <%= if @message do %>
              {render_packet_details(@message, @myself, @payload_view_type)}
            <% else %>
              <div class="text-center text-gray-500 py-8">
                <.icon name="hero-document" class="size-12 mx-auto mb-4 opacity-50" />
                <p>No message selected</p>
              </div>
            <% end %>
          </div>
        </div>
      </dialog>
    </div>
    """
  end

  # Render packet details based on packet type - copied from TraceGridComponent
  defp render_packet_details(message, myself, payload_view_type) do
    assigns = %{message: message, myself: myself, payload_view_type: payload_view_type}

    ~H"""
    <div class="space-y-3">
      <!-- Header Card with Essential Info -->
      <div class="card card-compact bg-base-200 shadow-sm">
        <div class="card-body p-3">
          <!-- Primary Info Row -->
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center gap-3">
              <div class="flex items-center gap-2">
                <.icon name={direction_icon(@message.direction)} class="size-4 text-base-content/70" />
                <span class={"badge badge-sm #{direction_badge_class(@message.direction)}"}>
                  {@message.direction || "N/A"}
                </span>
              </div>
              <div class="divider divider-horizontal mx-0"></div>
              <span class={"badge badge-sm #{message_type_badge_class(@message.type)}"}>
                {@message.type}
              </span>
            </div>
            <div class="text-xs text-base-content/60 font-mono">
              ID: {@message.packet_id || "N/A"}
            </div>
          </div>
          
    <!-- Secondary Info Row -->
          <div class="flex items-center justify-between text-xs">
            <div class="flex items-center gap-3">
              <span class="badge badge-ghost badge-sm">
                {@message.client_id || "unknown"}
              </span>
              <span class="text-base-content/60">
                {format_timestamp(@message.timestamp)}
              </span>
            </div>
            <%= if @message.data_size || @message.payload_size do %>
              <span class="text-base-content/60">
                {format_size_display(@message.payload_size, @message.data_size)}
              </span>
            <% end %>
          </div>
        </div>
      </div>
      
    <!-- Packet-specific Details Card -->
      <div class="card card-compact bg-base-100 border border-base-300">
        <div class="card-body p-3">
          <%= case @message.type do %>
            <% "PUBLISH" -> %>
              {render_publish_details(@message, @myself, @payload_view_type)}
            <% "SUBSCRIBE" -> %>
              {render_subscribe_details(@message)}
            <% "UNSUBSCRIBE" -> %>
              {render_unsubscribe_details(@message)}
            <% "SUBACK" -> %>
              {render_suback_details(@message)}
            <% "UNSUBACK" -> %>
              {render_unsuback_details(@message)}
            <% type when type in ["PUBACK", "PUBREC", "PUBREL", "PUBCOMP"] -> %>
              {render_pub_ack_details(@message)}
            <% "CONNACK" -> %>
              {render_connack_details(@message)}
            <% "CONNECT" -> %>
              {render_connect_details(@message, @myself, @payload_view_type)}
            <% "DISCONNECT" -> %>
              {render_disconnect_details(@message)}
            <% "CONNECTION_ERROR" -> %>
              {render_connection_error_details(@message)}
            <% "PINGREQ" -> %>
              {render_ping_details(@message)}
            <% "PINGRESP" -> %>
              {render_ping_details(@message)}
            <% _ -> %>
              {render_generic_details(@message, @myself, @payload_view_type)}
          <% end %>
        </div>
      </div>
      
    <!-- MQTT Properties Card (if present) -->
      <%= if is_map(@message.properties) && map_size(@message.properties) > 0 do %>
        <div class="card card-compact bg-base-100 border border-base-300">
          <div class="card-body p-3">
            <div class="flex items-center gap-2 mb-2">
              <.icon name="hero-cog-6-tooth" class="size-4 text-base-content/70" />
              <h4 class="text-sm font-medium text-base-content">MQTT Properties</h4>
            </div>
            <div class="bg-base-200 p-2 rounded-lg">
              <pre class="text-xs font-mono text-base-content leading-relaxed whitespace-pre-wrap">{format_mqtt_properties(@message.properties)}</pre>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Helper functions for rendering different packet types
  # These are simplified versions of the functions from TraceGridComponent

  defp render_publish_details(message, myself, payload_view_type) do
    assigns = %{message: message, myself: myself, payload_view_type: payload_view_type}

    ~H"""
    <div class="space-y-3">
      <!-- Topic Section -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-chat-bubble-left-right" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Topic</h4>
        </div>
        <div class="bg-base-200 p-2 rounded-lg">
          <code class="text-sm font-mono text-base-content break-all">
            {@message.topic || "N/A"}
          </code>
        </div>
      </div>
      
    <!-- QoS and Flags -->
      <div class="grid grid-cols-3 gap-3">
        <div class="stat stat-compact bg-base-200 rounded-lg">
          <div class="stat-title text-xs">QoS</div>
          <div class="stat-value text-sm">{@message.qos || 0}</div>
        </div>
        <div class="stat stat-compact bg-base-200 rounded-lg">
          <div class="stat-title text-xs">Retain</div>
          <div class="stat-value text-sm">{if @message.retain, do: "Yes", else: "No"}</div>
        </div>
        <div class="stat stat-compact bg-base-200 rounded-lg">
          <div class="stat-title text-xs">DUP</div>
          <div class="stat-value text-sm">
            {if Map.get(@message, :dup, false), do: "Yes", else: "No"}
          </div>
        </div>
      </div>
      
    <!-- Payload Section -->
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <.icon name="hero-document-text" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Payload</h4>
            <%= if (@message.payload_size && @message.payload_size > 0) || (@message.payload && String.length(@message.payload) > 0) do %>
              <button
                class="btn btn-xs btn-ghost"
                phx-click="copy_payload"
                phx-value-payload={@message.payload || ""}
                title="Copy payload"
              >
                <.icon name="hero-document-duplicate" class="size-3" />
              </button>
            <% end %>
          </div>
          <div class="tabs tabs-boxed tabs-xs">
            <button
              class={"tab tab-xs #{if @payload_view_type == "plaintext", do: "tab-active", else: ""}"}
              phx-click="switch_payload_view"
              phx-value-type="plaintext"
            >
              Text
            </button>
            <button
              class={"tab tab-xs #{if @payload_view_type == "json", do: "tab-active", else: ""}"}
              phx-click="switch_payload_view"
              phx-value-type="json"
            >
              JSON
            </button>
            <button
              class={"tab tab-xs #{if @payload_view_type == "hex", do: "tab-active", else: ""}"}
              phx-click="switch_payload_view"
              phx-value-type="hex"
            >
              Hex
            </button>
          </div>
        </div>

        <%= if (@message.payload_size && @message.payload_size > 0) || (@message.payload && String.length(@message.payload) > 0) do %>
          <%= if @payload_view_type == "json" do %>
            <div class="bg-base-100 rounded-lg">
              <.render_json_viewer
                payload={@message.payload || ""}
                id={"publish-payload-#{@message.id}"}
              />
            </div>
          <% else %>
            <pre class="text-xs leading-relaxed px-4 py-2 max-h-32 overflow-y-auto whitespace-pre-wrap bg-base-100 rounded-lg"><%= format_payload_for_display(@message.payload || "", @payload_view_type || "plaintext") %></pre>
          <% end %>
        <% else %>
          <div class="alert alert-info alert-sm">
            <.icon name="hero-information-circle" class="size-4" />
            <span class="text-xs">
              <%= if (@message.payload_size || 0) == 0 do %>
                Empty payload (0 bytes)
              <% else %>
                No payload data available
              <% end %>
            </span>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  # Simplified render functions for other packet types
  defp render_subscribe_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-plus-circle" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Topic Filters</h4>
        </div>
        <div class="bg-base-200 p-2 rounded-lg">
          <code class="text-sm font-mono text-base-content break-all">
            {@message.topic || "N/A"}
          </code>
        </div>
      </div>
      
    <!-- Subscription Details with MQTT Options -->
      <%= if @message.extra_info && is_list(@message.extra_info) do %>
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-list-bullet" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Subscription Details</h4>
          </div>
          <div class="space-y-2">
            <%= for {topic_filter, index} <- Enum.with_index(@message.extra_info) do %>
              <div class="card card-compact bg-base-100 border border-base-300">
                <div class="card-body p-3">
                  <div class="flex justify-between items-start mb-2">
                    <span class="text-xs font-medium text-base-content/70">Filter {index + 1}</span>
                  </div>
                  
    <!-- Topic Filter -->
                  <%= case topic_filter do %>
                    <% [topic, opts] when is_binary(topic) and is_map(opts) -> %>
                      <div class="space-y-2">
                        <div>
                          <span class="text-xs font-medium text-base-content/70">Topic:</span>
                          <code class="text-xs font-mono text-base-content break-all ml-2">
                            {topic}
                          </code>
                        </div>
                        <div class="flex flex-wrap gap-1">
                          <span class="badge badge-xs badge-primary">
                            QoS {Map.get(opts, :qos, 0)}
                          </span>
                          <%= if is_mqtt5_message?(@message) && Map.has_key?(opts, :rh) do %>
                            <span class="badge badge-xs badge-secondary">
                              RH:{Map.get(opts, :rh)}
                            </span>
                          <% end %>
                          <%= if is_mqtt5_message?(@message) && Map.get(opts, :rap, 0) != 0 do %>
                            <span class="badge badge-xs badge-warning">RAP</span>
                          <% end %>
                          <%= if is_mqtt5_message?(@message) && Map.get(opts, :nl, 0) != 0 do %>
                            <span class="badge badge-xs badge-info">NL</span>
                          <% end %>
                        </div>
                      </div>
                    <% [topic, opts] when is_binary(topic) and is_list(opts) -> %>
                      <div class="space-y-2">
                        <div>
                          <span class="text-xs font-medium text-base-content/70">Topic:</span>
                          <code class="text-xs font-mono text-base-content break-all ml-2">
                            {topic}
                          </code>
                        </div>
                        <div class="flex flex-wrap gap-1">
                          <%= for {key, value} <- opts do %>
                            <%= case key do %>
                              <% :qos -> %>
                                <span class="badge badge-xs badge-primary">QoS {value}</span>
                              <% :rh -> %>
                                <%= if is_mqtt5_message?(@message) do %>
                                  <span class="badge badge-xs badge-secondary">RH:{value}</span>
                                <% end %>
                              <% :rap -> %>
                                <%= if is_mqtt5_message?(@message) && value != 0 do %>
                                  <span class="badge badge-xs badge-warning">RAP</span>
                                <% end %>
                              <% :nl -> %>
                                <%= if is_mqtt5_message?(@message) && value != 0 do %>
                                  <span class="badge badge-xs badge-info">NL</span>
                                <% end %>
                              <% _ -> %>
                            <% end %>
                          <% end %>
                        </div>
                      </div>
                    <% topic when is_binary(topic) -> %>
                      <div>
                        <span class="text-xs font-medium text-base-content/70">Topic:</span>
                        <code class="text-xs font-mono text-base-content break-all ml-2">
                          {topic}
                        </code>
                      </div>
                    <% x -> %>
                      <div class="text-xs text-base-content/50">
                        {inspect(x)} Unknown filter format
                      </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  defp render_unsubscribe_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-minus-circle" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Topic Filters</h4>
        </div>
        <div class="bg-base-200 p-2 rounded-lg">
          <code class="text-sm font-mono text-base-content break-all">
            {@message.topic || "N/A"}
          </code>
        </div>
      </div>
      
    <!-- Unsubscription Details -->
      <%= if @message.extra_info && is_list(@message.extra_info) do %>
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-list-bullet" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Unsubscription Details</h4>
          </div>
          <div class="space-y-2">
            <%= for {topic_filter, index} <- Enum.with_index(@message.extra_info) do %>
              <div class="card card-compact bg-base-100 border border-base-300">
                <div class="card-body p-3">
                  <div class="flex justify-between items-start mb-2">
                    <span class="text-xs font-medium text-base-content/70">Filter {index + 1}</span>
                  </div>
                  
    <!-- Topic Filter -->
                  <%= case topic_filter do %>
                    <% [topic, _opts] when is_binary(topic) -> %>
                      <div>
                        <span class="text-xs font-medium text-base-content/70">Topic:</span>
                        <code class="text-xs font-mono text-base-content break-all ml-2">
                          {topic}
                        </code>
                      </div>
                    <% topic when is_binary(topic) -> %>
                      <div>
                        <span class="text-xs font-medium text-base-content/70">Topic:</span>
                        <code class="text-xs font-mono text-base-content break-all ml-2">
                          {topic}
                        </code>
                      </div>
                    <% _ -> %>
                      <div class="text-xs text-base-content/50">Unknown filter format</div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  defp render_suback_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <!-- Status Overview -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-check-circle" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Subscription Status</h4>
        </div>
        <div class="alert alert-sm">
          <span class={get_ack_status_class(@message.reason_code)}>
            {format_suback_status(@message.reason_code)}
          </span>
        </div>
      </div>
      
    <!-- Reason Codes Details -->
      <%= if @message.reason_code && is_list(@message.reason_code) do %>
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-list-bullet" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Reason Codes</h4>
          </div>
          <div class="space-y-1">
            <%= for {code, index} <- Enum.with_index(@message.reason_code) do %>
              <div class={"card card-compact #{get_reason_code_class(code)}"}>
                <div class="card-body p-2">
                  <div class="flex justify-between items-center">
                    <span class="text-xs font-medium">Filter {index + 1}</span>
                    <code class="text-xs font-mono">{format_reason_code(code)}</code>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  defp render_unsuback_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <!-- Status Overview -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-x-circle" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Unsubscription Status</h4>
        </div>
        <div class="alert alert-sm">
          <span class={get_ack_status_class(@message.reason_code)}>
            {format_unsuback_status(@message.reason_code)}
          </span>
        </div>
      </div>
      
    <!-- Reason Codes Details -->
      <%= if @message.reason_code && is_list(@message.reason_code) do %>
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-list-bullet" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Reason Codes</h4>
          </div>
          <div class="space-y-1">
            <%= for {code, index} <- Enum.with_index(@message.reason_code) do %>
              <div class={"card card-compact #{get_reason_code_class(code)}"}>
                <div class="card-body p-2">
                  <div class="flex justify-between items-center">
                    <span class="text-xs font-medium">Filter {index + 1}</span>
                    <code class="text-xs font-mono">{format_reason_code(code)}</code>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  defp render_pub_ack_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <!-- Status Overview -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-check-badge" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Acknowledgment Status</h4>
        </div>
        <div class="alert alert-sm">
          <span class={get_ack_status_class(@message.reason_code)}>
            {format_pub_ack_status(@message.reason_code)}
          </span>
        </div>
      </div>
      
    <!-- Reason Code Details -->
      <%= if @message.reason_code do %>
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-information-circle" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Reason Code</h4>
          </div>
          <div class={"card card-compact #{get_reason_code_class(@message.reason_code)}"}>
            <div class="card-body p-2">
              <code class="text-xs font-mono">{format_reason_code(@message.reason_code)}</code>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  defp render_connack_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <!-- Status Overview -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-signal" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Connection Status</h4>
        </div>
        <div class="alert alert-sm">
          <span class={get_ack_status_class(@message.reason_code)}>
            {format_connack_status(@message.reason_code)}
          </span>
        </div>
      </div>
      
    <!-- Reason Code Details -->
      <%= if @message.reason_code do %>
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-information-circle" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Reason Code</h4>
          </div>
          <div class={"card card-compact #{get_reason_code_class(@message.reason_code)}"}>
            <div class="card-body p-2">
              <code class="text-xs font-mono">{format_reason_code(@message.reason_code)}</code>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  defp render_connect_details(message, myself, payload_view_type) do
    assigns = %{message: message, myself: myself, payload_view_type: payload_view_type}

    ~H"""
    <div class="space-y-3">
      <%= if @message.extra_info && is_map(@message.extra_info) do %>
        <!-- Protocol Information -->
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-signal" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Protocol Information</h4>
          </div>
          <div class="grid grid-cols-2 gap-3">
            <div class="stat stat-compact bg-base-200 rounded-lg">
              <div class="stat-title text-xs">Protocol</div>
              <div class="stat-value text-sm">{@message.extra_info.proto_name || "N/A"}</div>
            </div>
            <div class="stat stat-compact bg-base-200 rounded-lg">
              <div class="stat-title text-xs">Version</div>
              <div class="stat-value text-sm">{@message.extra_info.proto_ver || "N/A"}</div>
            </div>
          </div>
        </div>
        
    <!-- Connection Parameters -->
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-cog-6-tooth" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Connection Parameters</h4>
          </div>
          <div class="grid grid-cols-3 gap-3">
            <div class="stat stat-compact bg-base-200 rounded-lg">
              <div class="stat-title text-xs">Keep Alive</div>
              <div class="stat-value text-sm">{@message.extra_info.keepalive || 0}s</div>
            </div>
            <div class="stat stat-compact bg-base-200 rounded-lg">
              <div class="stat-title text-xs">Clean Start</div>
              <div class="stat-value text-sm">{format_boolean(@message.extra_info.clean_start)}</div>
            </div>
            <div class="stat stat-compact bg-base-200 rounded-lg">
              <div class="stat-title text-xs">Bridge</div>
              <div class="stat-value text-sm">{format_boolean(@message.extra_info.is_bridge)}</div>
            </div>
          </div>
        </div>
        
    <!-- Client Information -->
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-user" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Client Information</h4>
          </div>
          <div class="space-y-2">
            <div class="bg-base-200 p-2 rounded-lg">
              <div class="text-xs text-base-content/60 mb-1">Client ID</div>
              <code class="text-sm font-mono text-base-content break-all">
                {@message.extra_info.clientid || "N/A"}
              </code>
            </div>
            <div class="grid grid-cols-2 gap-3">
              <div class="stat stat-compact bg-base-200 rounded-lg">
                <div class="stat-title text-xs">Username</div>
                <div class="stat-value text-sm">
                  {format_auth_field(@message.extra_info.username)}
                </div>
              </div>
              <div class="stat stat-compact bg-base-200 rounded-lg">
                <div class="stat-title text-xs">Password</div>
                <div class="stat-value text-sm">
                  {format_auth_field(@message.extra_info.password)}
                </div>
              </div>
            </div>
          </div>
        </div>
        
    <!-- Will Message Information -->
        <%= if @message.extra_info.will_flag do %>
          <div class="space-y-2">
            <div class="flex items-center gap-2">
              <.icon name="hero-document-text" class="size-4 text-base-content/70" />
              <h4 class="text-sm font-medium text-base-content">Will Message</h4>
            </div>
            <div class="space-y-2">
              <%= if @message.extra_info.will_topic && @message.extra_info.will_topic != "" do %>
                <div class="bg-base-200 p-2 rounded-lg">
                  <div class="text-xs text-base-content/60 mb-1">Will Topic</div>
                  <code class="text-sm font-mono text-base-content break-all">
                    {@message.extra_info.will_topic}
                  </code>
                </div>
              <% end %>
              <div class="grid grid-cols-2 gap-3">
                <div class="stat stat-compact bg-base-200 rounded-lg">
                  <div class="stat-title text-xs">Will QoS</div>
                  <div class="stat-value text-sm">{@message.extra_info.will_qos || 0}</div>
                </div>
                <div class="stat stat-compact bg-base-200 rounded-lg">
                  <div class="stat-title text-xs">Will Retain</div>
                  <div class="stat-value text-sm">
                    {format_boolean(@message.extra_info.will_retain)}
                  </div>
                </div>
              </div>
              <%= if @message.extra_info.will_payload && @message.extra_info.will_payload != "" do %>
                <div class="bg-base-200 p-2 rounded-lg">
                  <div class="text-xs text-base-content/60 mb-1">Will Payload</div>
                  <pre class="text-xs leading-relaxed whitespace-pre-wrap font-mono text-base-content max-h-20 overflow-y-auto">{@message.extra_info.will_payload}</pre>
                </div>
              <% end %>
            </div>
          </div>
        <% else %>
          <div class="space-y-2">
            <div class="flex items-center gap-2">
              <.icon name="hero-document-text" class="size-4 text-base-content/70" />
              <h4 class="text-sm font-medium text-base-content">Will Message</h4>
            </div>
            <div class="alert alert-info alert-sm">
              <.icon name="hero-information-circle" class="size-4" />
              <span class="text-xs">No will message configured</span>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end

  defp render_disconnect_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-x-mark" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Disconnection</h4>
        </div>
        <%= if @message.reason_code do %>
          <div class="bg-base-200 p-2 rounded-lg">
            <code class="text-sm font-mono">{format_reason_code(@message.reason_code)}</code>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  defp render_connection_error_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <!-- Error Status -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-exclamation-triangle" class="size-4 text-error" />
          <h4 class="text-sm font-medium text-base-content">Connection Error</h4>
        </div>
        <div class="alert alert-error alert-sm">
          <.icon name="hero-x-circle" class="size-4" />
          <span class="text-xs">
            {Mqttable.MqttPacketProcessor.format_connection_error_payload(@message.payload)}
          </span>
        </div>
      </div>
      
    <!-- Technical Details (if available) -->
      <%= if @message.extra_info && @message.extra_info.error_reason do %>
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-code-bracket" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Technical Details</h4>
          </div>
          <div class="bg-base-200 p-2 rounded-lg">
            <code class="text-xs font-mono text-base-content/80">
              {@message.extra_info.error_reason}
            </code>
          </div>
        </div>
      <% end %>
      
    <!-- Troubleshooting Tips -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-light-bulb" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Troubleshooting</h4>
        </div>
        <div class="bg-base-100 p-3 rounded-lg border border-base-300">
          <ul class="text-xs space-y-1 text-base-content/80">
            <li>• Check if the MQTT broker is running and accessible</li>
            <li>• Verify the broker host and port configuration</li>
            <li>• Ensure network connectivity to the broker</li>
            <li>• Check firewall settings and security groups</li>
          </ul>
        </div>
      </div>
    </div>
    """
  end

  defp render_ping_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-heart" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Keep Alive</h4>
        </div>
        <div class="alert alert-info alert-sm">
          <.icon name="hero-information-circle" class="size-4" />
          <span class="text-xs">
            {String.upcase(@message.type)} packet for connection keep-alive
          </span>
        </div>
      </div>
    </div>
    """
  end

  defp render_generic_details(message, myself, payload_view_type) do
    assigns = %{message: message, myself: myself, payload_view_type: payload_view_type}

    ~H"""
    <div class="space-y-3">
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-document" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Packet Information</h4>
        </div>
        <div class="alert alert-warning alert-sm">
          <.icon name="hero-exclamation-triangle" class="size-4" />
          <span class="text-xs">
            Unknown or unsupported packet type: {@message.type}
          </span>
        </div>
      </div>
    </div>
    """
  end

  # Helper functions - import from TraceGridComponent or define simplified versions
  defp direction_icon("IN"), do: "hero-arrow-down-circle"
  defp direction_icon("OUT"), do: "hero-arrow-up-circle"
  defp direction_icon(_), do: "hero-minus-circle"

  defp direction_badge_class("IN"), do: "badge-success"
  defp direction_badge_class("OUT"), do: "badge-warning"
  defp direction_badge_class(_), do: "badge-neutral"

  defp message_type_badge_class("PUBLISH"), do: "badge-primary"
  defp message_type_badge_class("SUBSCRIBE"), do: "badge-secondary"
  defp message_type_badge_class("CONNECT"), do: "badge-success"
  defp message_type_badge_class(_), do: "badge-neutral"

  defp format_timestamp(timestamp) when is_integer(timestamp) do
    time_zone = Mqttable.Settings.get_time_zone()

    DateTime.from_unix!(timestamp, :microsecond)
    |> DateTime.shift_zone!(time_zone)
    |> Calendar.strftime("%m-%d %H:%M:%S.%3f")
    |> String.slice(0..17)
  end

  defp format_timestamp(_), do: "N/A"

  # Helper functions for error highlighting and status formatting

  # Get CSS class for ACK status (success/error highlighting)
  defp get_ack_status_class(reason_code) when is_integer(reason_code) do
    if reason_code == 0 do
      "text-xs badge badge-success"
    else
      "text-xs badge badge-error"
    end
  end

  defp get_ack_status_class(reason_codes) when is_list(reason_codes) do
    if Enum.all?(reason_codes, &(&1 == 0)) do
      "text-xs badge badge-success"
    else
      "text-xs badge badge-error"
    end
  end

  defp get_ack_status_class(_), do: "text-xs badge badge-ghost"

  # Get CSS class for individual reason codes
  defp get_reason_code_class(reason_code) when is_integer(reason_code) do
    if reason_code == 0 do
      "bg-success/20 text-success"
    else
      "bg-error/20 text-error"
    end
  end

  defp get_reason_code_class(_), do: "bg-base-200 text-base-content"

  # Format SUBACK status
  defp format_suback_status(reason_codes) when is_list(reason_codes) do
    success_count = Enum.count(reason_codes, &(&1 == 0))
    total_count = length(reason_codes)

    cond do
      success_count == total_count ->
        "All Subscriptions Successful"

      success_count == 0 ->
        # All failed - show specific error message
        first_error = Enum.find(reason_codes, &(&1 != 0))
        error_message = format_reason_code_name(first_error || 0)
        "Subscription Failed: #{error_message}"

      true ->
        "#{success_count}/#{total_count} Subscriptions Successful"
    end
  end

  defp format_suback_status(_), do: "Unknown"

  # Format UNSUBACK status
  defp format_unsuback_status(reason_codes) when is_list(reason_codes) do
    success_count = Enum.count(reason_codes, &(&1 == 0))
    total_count = length(reason_codes)

    cond do
      success_count == total_count ->
        "All Unsubscriptions Successful"

      success_count == 0 ->
        # All failed - show specific error message
        first_error = Enum.find(reason_codes, &(&1 != 0))
        error_message = format_reason_code_name(first_error || 0)
        "Unsubscription Failed: #{error_message}"

      true ->
        "#{success_count}/#{total_count} Unsubscriptions Successful"
    end
  end

  defp format_unsuback_status(_), do: "Unknown"

  # Format PUBACK/PUBREC/PUBREL/PUBCOMP status
  defp format_pub_ack_status(reason_code) when is_integer(reason_code) do
    case reason_code do
      0 -> "Success"
      16 -> "No matching subscribers"
      128 -> "Unspecified error"
      129 -> "Implementation specific error"
      135 -> "Not authorized"
      144 -> "Topic Name invalid"
      145 -> "Packet identifier in use"
      146 -> "Packet identifier not found"
      147 -> "Receive Maximum exceeded"
      148 -> "Topic Alias invalid"
      149 -> "Packet too large"
      150 -> "Message rate too high"
      151 -> "Quota exceeded"
      152 -> "Administrative action"
      153 -> "Payload format invalid"
      154 -> "Retain not supported"
      155 -> "QoS not supported"
      156 -> "Use another server"
      157 -> "Server moved"
      158 -> "Shared Subscriptions not supported"
      159 -> "Connection rate exceeded"
      160 -> "Maximum connect time"
      161 -> "Subscription Identifiers not supported"
      162 -> "Wildcard Subscriptions not supported"
      _ -> "Unknown (#{reason_code})"
    end
  end

  defp format_pub_ack_status(_), do: "Unknown"

  # Format CONNACK status
  defp format_connack_status(reason_code) when is_integer(reason_code) do
    case reason_code do
      0 -> "Connection Accepted"
      128 -> "Unspecified error"
      129 -> "Malformed Packet"
      130 -> "Protocol Error"
      131 -> "Implementation specific error"
      132 -> "Unsupported Protocol Version"
      133 -> "Client Identifier not valid"
      134 -> "Bad User Name or Password"
      135 -> "Not authorized"
      136 -> "Server unavailable"
      137 -> "Server busy"
      138 -> "Banned"
      140 -> "Bad authentication method"
      _ -> "Connection Refused (#{reason_code})"
    end
  end

  defp format_connack_status(_), do: "Unknown"

  # Format reason codes with descriptive text
  defp format_reason_code(reason_code) when is_integer(reason_code) do
    case reason_code do
      0 -> "0x00 - Success"
      1 -> "0x01 - Normal disconnection"
      2 -> "0x02 - Granted QoS 0"
      4 -> "0x04 - Disconnect with Will Message"
      16 -> "0x10 - No matching subscribers"
      17 -> "0x11 - No subscription existed"
      24 -> "0x18 - Continue authentication"
      25 -> "0x19 - Re-authenticate"
      128 -> "0x80 - Unspecified error"
      129 -> "0x81 - Malformed Packet"
      130 -> "0x82 - Protocol Error"
      131 -> "0x83 - Implementation specific error"
      132 -> "0x84 - Unsupported Protocol Version"
      133 -> "0x85 - Client Identifier not valid"
      134 -> "0x86 - Bad User Name or Password"
      135 -> "0x87 - Not authorized"
      136 -> "0x88 - Server unavailable"
      137 -> "0x89 - Server busy"
      138 -> "0x8A - Banned"
      140 -> "0x8C - Bad authentication method"
      144 -> "0x90 - Topic Name invalid"
      149 -> "0x95 - Packet too large"
      151 -> "0x97 - Quota exceeded"
      153 -> "0x99 - Payload format invalid"
      154 -> "0x9A - Retain not supported"
      155 -> "0x9B - QoS not supported"
      156 -> "0x9C - Use another server"
      157 -> "0x9D - Server moved"
      158 -> "0x9E - Shared Subscriptions not supported"
      159 -> "0x9F - Connection rate exceeded"
      160 -> "0xA0 - Maximum connect time"
      161 -> "0xA1 - Subscription Identifiers not supported"
      162 -> "0xA2 - Wildcard Subscriptions not supported"
      _ -> "0x#{Integer.to_string(reason_code, 16) |> String.upcase()} - Unknown"
    end
  end

  defp format_reason_code(code), do: to_string(code)

  # Format reason code name only (without hex code)
  defp format_reason_code_name(reason_code) when is_integer(reason_code) do
    case reason_code do
      0 -> "Success"
      128 -> "Unspecified error"
      129 -> "Implementation specific error"
      131 -> "Implementation specific error"
      135 -> "Not authorized"
      143 -> "Topic filter invalid"
      145 -> "Packet identifier in use"
      151 -> "Quota exceeded"
      158 -> "Shared subscriptions not supported"
      161 -> "Subscription identifiers not supported"
      162 -> "Wildcard subscriptions not supported"
      _ -> "Error (#{reason_code})"
    end
  end

  defp format_reason_code_name(code), do: "Error (#{code})"

  defp format_payload_for_display(payload, "hex") when is_binary(payload) do
    payload
    |> :binary.bin_to_list()
    |> Enum.map(&Integer.to_string(&1, 16))
    |> Enum.map(&String.pad_leading(&1, 2, "0"))
    |> Enum.chunk_every(16)
    |> Enum.map(&Enum.join(&1, " "))
    |> Enum.join("\n")
  end

  defp format_payload_for_display(payload, _), do: payload || ""

  # Helper function to format MQTT properties for display
  defp format_mqtt_properties(properties) when is_map(properties) do
    properties
    |> Enum.map(fn {key, value} -> "#{key}: #{inspect(value)}" end)
    |> Enum.join("\n")
  end

  defp format_mqtt_properties(_), do: "N/A"

  # Check if payload is valid JSON
  defp is_valid_json?(payload) when is_binary(payload) do
    case Jason.decode(payload) do
      {:ok, _} -> true
      {:error, _} -> false
    end
  end

  defp is_valid_json?(_), do: false

  # Render JSON viewer component
  defp render_json_viewer(assigns) do
    ~H"""
    <%= if is_valid_json?(@payload) do %>
      <div
        id={"json-viewer-#{@id}"}
        class="json-viewer-container"
        phx-hook="JsonViewer"
        data-json={@payload}
      >
        <!-- JSON viewer will be rendered here by the hook -->
      </div>
    <% else %>
      <div class="json-viewer-error">
        <div class="json-error-header">Invalid JSON Data</div>
        <pre class="json-raw-content"><%= @payload %></pre>
      </div>
    <% end %>
    """
  end

  # Helper function to format boolean values for display
  defp format_boolean(true), do: "Yes"
  defp format_boolean(false), do: "No"
  defp format_boolean(nil), do: "No"
  defp format_boolean(_), do: "N/A"

  # Helper function to format authentication fields (username/password)
  defp format_auth_field(nil), do: "Not set"
  defp format_auth_field(""), do: "Not set"
  defp format_auth_field(value) when is_binary(value), do: "Set"
  defp format_auth_field(_), do: "N/A"

  # Format size display for payload_size/data_size (no units)
  defp format_size_display(payload_size, data_size) do
    payload_size = payload_size || 0
    data_size = data_size || 0

    cond do
      payload_size == 0 and data_size == 0 ->
        "-"

      payload_size == data_size ->
        "#{data_size} bytes"

      true ->
        "#{payload_size}/#{data_size} bytes"
    end
  end

  # Helper function to determine if a message is from an MQTT 5.0 connection
  defp is_mqtt5_message?(message) do
    cond do
      # Check if message has MQTT 5.0 properties (non-empty properties map)
      is_map(message.properties) && map_size(message.properties) > 0 ->
        true

      # For CONNECT packets, check the protocol version in extra_info
      message.type == "CONNECT" && is_map(message.extra_info) ->
        case Map.get(message.extra_info, :proto_ver) do
          "5" -> true
          "5.0" -> true
          _ -> false
        end

      # For other packets, assume MQTT 3.x if no properties
      true ->
        false
    end
  end
end
