defmodule MqttableWeb.PayloadEditorDemoLive do
  @moduledoc """
  Demo page for testing the PayloadEditorComponent with side-by-side layout.
  """

  use MqttableWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:payload, "")
      |> assign(:payload_format, "text")
      |> assign(:page_title, "Payload Editor Demo")

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="container mx-auto p-6">
      <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Payload Editor Demo</h1>
        <p class="text-gray-600">
          Test the new side-by-side layout with horizontal tabs and live preview.
        </p>
      </div>

      <div class="bg-white rounded-lg shadow-lg p-6">
        <.live_component
          module={MqttableWeb.PayloadEditorComponent}
          id="demo-payload-editor"
          payload={@payload}
          payload_format={@payload_format}
          label="Demo Payload Editor"
        />
      </div>

      <div class="mt-6 bg-gray-50 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-2">Current State:</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <strong>Payload:</strong>
            <pre class="bg-white p-2 rounded border mt-1 text-xs"><%= @payload %></pre>
          </div>
          <div>
            <strong>Format:</strong>
            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded"><%= @payload_format %></span>
          </div>
        </div>
      </div>

      <div class="mt-6 bg-blue-50 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-2">Features to Test:</h3>
        <ul class="list-disc list-inside space-y-1 text-sm">
          <li>Horizontal tabs: Text, JSON, Hex, File</li>
          <li>Side-by-side layout: Payload area | Live Preview area</li>
          <li>Equal height for both areas (300px)</li>
          <li>JSON viewer integration for JSON format</li>
          <li>Template syntax detection and preview</li>
          <li>Format-specific placeholders and styling</li>
        </ul>
      </div>
    </div>
    """
  end

  @impl true
  def handle_info({:payload_editor_changed, payload, payload_format}, socket) do
    socket =
      socket
      |> assign(:payload, payload)
      |> assign(:payload_format, payload_format)

    {:noreply, socket}
  end
end
