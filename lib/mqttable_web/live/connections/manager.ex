defmodule MqttableWeb.Connections.Manager do
  @moduledoc """
  Module for managing connections.
  This module provides functions for creating, updating, and deleting connections.
  """

  require Logger
  import Phoenix.Component
  import Phoenix.LiveView

  # Import verified routes
  use Phoenix.VerifiedRoutes,
    endpoint: MqttableWeb.Endpoint,
    router: MqttableWeb.Router,
    statics: MqttableWeb.static_paths()

  alias Mqttable.ConnectionSets
  alias Mqttable.Encryption
  alias MqttableWeb.Utils.ConnectionHelpers

  @doc """
  Handles the open_new_connection_modal event.
  """
  def handle_open_new_connection_modal(socket, %{"name" => set_name}) do
    # Find the connection set by name
    set = ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, set_name)

    if set do
      # Always create a new default connection with fresh random values
      default_connection = %{
        name: ConnectionHelpers.generate_random_connection_name(),
        client_id: "mqtt_" <> ConnectionHelpers.generate_random_string(8),
        username: "",
        password: "",
        mqtt_version: "5.0",
        connect_timeout: 45,
        keep_alive: 300,
        clean_start: true,
        session_expiry_interval: 0,
        receive_maximum: nil,
        maximum_packet_size: nil,
        topic_alias_maximum: nil,
        request_response_info: false,
        request_problem_info: false,
        user_properties: [%{key: "", value: ""}],
        will_topic: "",
        will_qos: "0",
        will_retain: false,
        will_payload: "",
        will_payload_format: false,
        will_delay_interval: 0,
        will_message_expiry: 0,
        will_content_type: "",
        will_response_topic: "",
        will_correlation_data: "",
        topics: [],
        connection_time: nil,
        # 默认状态为断开连接
        status: "disconnected"
      }

      # Mark this change as initiated locally to prevent loops
      socket = assign(socket, :active_set_change_source, "local")

      # Update the active connection set in the UI state to broadcast to all clients
      ConnectionSets.update_active_connection_set(set_name)

      # Open the new connection modal and set the connection set as active
      socket =
        socket
        |> assign(:show_modal, true)
        |> assign(:modal_type, :new_connection)
        |> assign(:edit_connection, default_connection)
        |> assign(:edit_connection_index, nil)
        |> assign(:connection_set, set)
        |> assign(:active_connection_set, set)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handles the open_edit_connection_modal event.
  """
  def handle_open_edit_connection_modal(
        socket,
        %{"set_name" => set_name, "client_id" => client_id}
      ) do
    # Find the connection set by name
    set = ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, set_name)

    if set do
      # Find the connection by client_id and get its index
      connections = Map.get(set, :connections, [])

      {connection, index} =
        connections
        |> Enum.with_index()
        |> Enum.find(fn {conn, _index} -> conn.client_id == client_id end)
        |> case do
          {conn, idx} -> {conn, idx}
          nil -> {nil, nil}
        end

      if connection do
        # Mark this change as initiated locally to prevent loops
        socket = assign(socket, :active_set_change_source, "local")

        # Update the active connection set in the UI state to broadcast to all clients
        ConnectionSets.update_active_connection_set(set_name)

        # Open the edit connection modal and set the connection set as active
        socket =
          socket
          |> assign(:show_modal, true)
          |> assign(:modal_type, :edit_connection)
          |> assign(:edit_connection, connection)
          |> assign(:edit_connection_index, index)
          |> assign(:connection_set, set)
          |> assign(:active_connection_set, set)

        {:noreply, socket}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handles the generate_client_id event.
  """
  def handle_generate_client_id(socket) do
    # Get the current connection set to check for uniqueness
    connection_set = socket.assigns.connection_set
    existing_connections = Map.get(connection_set, :connections, [])

    # Generate a unique client ID
    client_id = generate_unique_client_id(existing_connections)

    # Update the edit_connection in the socket
    edit_connection = Map.put(socket.assigns.edit_connection, :client_id, client_id)

    {:noreply, assign(socket, :edit_connection, edit_connection)}
  end

  @doc """
  Handles the generate_connection_name event.
  """
  def handle_generate_connection_name(socket) do
    # Get the current connection set to check for uniqueness
    connection_set = socket.assigns.connection_set
    existing_connections = Map.get(connection_set, :connections, [])

    # Generate a unique connection name
    name = generate_unique_connection_name(existing_connections)

    # Update the edit_connection in the socket
    edit_connection = Map.put(socket.assigns.edit_connection, :name, name)

    {:noreply, assign(socket, :edit_connection, edit_connection)}
  end

  @doc """
  Handles the save_and_connect_connection event.
  Saves the connection and immediately attempts to connect.
  """
  def handle_save_and_connect_connection(socket, params) do
    %{"connection" => connection_params} = params
    # Get the connection set before saving (since save will set it to nil)
    connection_set = socket.assigns.connection_set
    client_id = connection_params["client_id"]

    # First save the connection using the existing logic
    {:noreply, updated_socket} =
      handle_save_connection(socket, params)

    # Check if save was successful by looking for flash errors
    flash_errors = updated_socket.assigns.flash[:error]

    if flash_errors do
      # Save failed (validation error), return the socket with error message
      {:noreply, updated_socket}
    else
      # Save was successful, attempt to connect
      # Get the updated connection sets from the socket
      updated_connection_sets = updated_socket.assigns.connection_sets

      # Find the updated connection set
      updated_connection_set =
        Enum.find(updated_connection_sets, fn set -> set.name == connection_set.name end)

      # Find the newly saved connection
      connections = Map.get(updated_connection_set, :connections, [])
      connection = Enum.find(connections, fn conn -> conn.client_id == client_id end)

      if connection do
        # Attempt to establish MQTT connection
        case Mqttable.MqttClient.Manager.connect(connection, connection_set) do
          {:ok, _} ->
            # Connection successful, close modal and show success message
            {:noreply,
             updated_socket
             |> assign(:show_modal, false)
             |> assign(:modal_type, nil)
             |> assign(:connection_set, nil)}

          {:error, reason, error_message} ->
            # Connection failed, close modal but don't show flash message
            # Error will be displayed in trace table instead
            Logger.error(
              "Failed to connect MQTT client #{client_id}: #{inspect(reason)} - #{error_message}"
            )

            {:noreply,
             updated_socket
             |> assign(:show_modal, false)
             |> assign(:modal_type, nil)
             |> assign(:connection_set, nil)}
        end
      else
        # Connection not found after save, this shouldn't happen
        {:noreply,
         updated_socket
         |> assign(:show_modal, false)
         |> assign(:modal_type, nil)
         |> assign(:connection_set, nil)
         |> put_flash(:error, "Failed to save connection")}
      end
    end
  end

  @doc """
  Handles the save_connection event.
  """
  def handle_save_connection(socket, params) do
    %{"connection" => connection_params} = params
    # Get the connection set
    connection_set = socket.assigns.connection_set

    # Validate client_id and name
    client_id = connection_params["client_id"]
    name = connection_params["name"]

    # Get existing connections for uniqueness validation
    existing_connections = Map.get(connection_set, :connections, [])

    cond do
      name == nil || name == "" ->
        {:noreply,
         socket
         |> put_flash(:error, "Name is required")}

      client_id == nil || client_id == "" ->
        {:noreply,
         socket
         |> put_flash(:error, "Client ID is required")}

      # Check if name is unique within the connection set
      Enum.any?(existing_connections, fn conn -> conn.name == name end) ->
        {:noreply,
         socket
         |> put_flash(:error, "Connection name '#{name}' already exists in this broker")}

      # Check if client_id is unique within the connection set
      Enum.any?(existing_connections, fn conn -> conn.client_id == client_id end) ->
        {:noreply,
         socket
         |> put_flash(:error, "Client ID '#{client_id}' already exists in this broker")}

      true ->
        # Process user properties from the new format (user_property_key_0, user_property_value_0, etc.)
        user_properties = parse_user_properties_from_form_params(params)
        Logger.debug("Final user_properties for save_connection: #{inspect(user_properties)}")

        # Create a new connection with the form data
        new_connection = %{
          name: connection_params["name"],
          client_id: client_id,
          username: connection_params["username"],
          password: Encryption.encrypt(connection_params["password"]),
          mqtt_version: connection_params["mqtt_version"] || "5.0",
          connect_timeout: ConnectionHelpers.parse_int(connection_params["connect_timeout"], 30),
          keep_alive: ConnectionHelpers.parse_int(connection_params["keep_alive"], 60),
          clean_start: connection_params["clean_start"] == "on",
          session_expiry_interval:
            ConnectionHelpers.parse_int(connection_params["session_expiry_interval"], 0),
          receive_maximum: ConnectionHelpers.parse_int(connection_params["receive_maximum"], 0),
          connection_time: nil,
          maximum_packet_size:
            ConnectionHelpers.parse_int(connection_params["maximum_packet_size"], 0),
          topic_alias_maximum:
            ConnectionHelpers.parse_int(connection_params["topic_alias_maximum"], 0),
          request_response_info: connection_params["request_response_info"] == "on",
          request_problem_info: connection_params["request_problem_info"] == "on",
          user_properties: user_properties,
          will_topic: connection_params["will_topic"],
          will_qos: connection_params["will_qos"] || "0",
          will_retain: connection_params["will_retain"] == "on",
          will_payload: connection_params["will_payload"],
          will_payload_format: connection_params["will_payload_format"] == "on",
          will_delay_interval:
            ConnectionHelpers.parse_int(connection_params["will_delay_interval"], 0),
          will_message_expiry:
            ConnectionHelpers.parse_int(connection_params["will_message_expiry"], 0),
          will_content_type: connection_params["will_content_type"],
          # 使用表单中的状态或默认为断开连接
          status: connection_params["status"] || "disconnected",
          will_response_topic: connection_params["will_response_topic"],
          will_correlation_data: connection_params["will_correlation_data"],
          # Initialize topics as an empty list
          topics: []
        }

        # Get existing connections or initialize an empty list
        connections = Map.get(connection_set, :connections, [])

        # Add the new connection to the set
        updated_connections = connections ++ [new_connection]
        updated_set = Map.put(connection_set, :connections, updated_connections)

        # Update the connection sets list
        connection_sets =
          Enum.map(socket.assigns.connection_sets, fn set ->
            if set.name == connection_set.name, do: updated_set, else: set
          end)

        # Save the updated connection sets to persistent storage
        MqttableWeb.ConnectionSets.Manager.save_connection_set(connection_sets)

        # Reset the connection form
        default_connection = %{
          name: ConnectionHelpers.generate_random_connection_name(),
          client_id: "mqtt_" <> ConnectionHelpers.generate_random_string(8),
          username: "",
          password: "",
          mqtt_version: "5.0",
          connect_timeout: 45,
          keep_alive: 300,
          clean_start: true,
          session_expiry_interval: 0,
          receive_maximum: nil,
          maximum_packet_size: nil,
          topic_alias_maximum: nil,
          request_response_info: false,
          request_problem_info: false,
          user_properties: [%{key: "", value: ""}],
          will_topic: "",
          will_qos: "0",
          will_retain: false,
          will_payload: "",
          will_payload_format: false,
          will_delay_interval: 0,
          will_message_expiry: 0,
          will_content_type: "",
          will_response_topic: "",
          will_correlation_data: "",
          topics: [],
          connection_time: nil
        }

        # Close the modal and update the socket
        {:noreply,
         socket
         |> assign(:connection_sets, connection_sets)
         |> assign(:edit_connection, default_connection)
         |> assign(:show_modal, false)
         |> assign(:modal_type, nil)
         |> assign(:connection_set, nil)}
    end
  end

  @doc """
  Handles the update_connection event.
  """
  def handle_update_connection(socket, params) do
    %{"connection" => connection_params, "old_client_id" => old_client_id} = params
    # Get the connection set
    connection_set = socket.assigns.connection_set

    # Find the existing connection to get its connection_time
    connections = Map.get(connection_set, :connections, [])
    connection = Enum.find(connections, fn conn -> conn.client_id == old_client_id end)

    # Validate client_id and name
    client_id = connection_params["client_id"]
    name = connection_params["name"]

    # Get existing connections for uniqueness validation (excluding the current one being edited)
    existing_connections =
      Map.get(connection_set, :connections, [])
      |> Enum.reject(fn conn -> conn.client_id == old_client_id end)

    cond do
      name == nil || name == "" ->
        {:noreply,
         socket
         |> put_flash(:error, "Name is required")}

      client_id == nil || client_id == "" ->
        {:noreply,
         socket
         |> put_flash(:error, "Client ID is required")}

      # Check if name is unique within the connection set (excluding current connection)
      Enum.any?(existing_connections, fn conn -> conn.name == name end) ->
        {:noreply,
         socket
         |> put_flash(:error, "Connection name '#{name}' already exists in this broker")}

      # Check if client_id is unique within the connection set (excluding current connection)
      Enum.any?(existing_connections, fn conn -> conn.client_id == client_id end) ->
        {:noreply,
         socket
         |> put_flash(:error, "Client ID '#{client_id}' already exists in this broker")}

      true ->
        # Check if the connection is currently connected before updating
        current_status = Mqttable.MqttClient.Manager.get_status(old_client_id)
        was_connected = current_status == :connected

        # Process user properties from the new format (user_property_key_0, user_property_value_0, etc.)
        # Note: user properties are in the top-level params, not inside connection_params
        user_properties = parse_user_properties_from_form_params(params)
        Logger.debug("Final user_properties for update_connection: #{inspect(user_properties)}")

        # Create an updated connection with the form data
        updated_connection = %{
          name: connection_params["name"],
          client_id: client_id,
          username: connection_params["username"],
          password: Encryption.encrypt(connection_params["password"]),
          mqtt_version: connection_params["mqtt_version"] || "5.0",
          connect_timeout: ConnectionHelpers.parse_int(connection_params["connect_timeout"], 45),
          keep_alive: ConnectionHelpers.parse_int(connection_params["keep_alive"], 60),
          clean_start: connection_params["clean_start"] == "on",
          session_expiry_interval:
            ConnectionHelpers.parse_int(connection_params["session_expiry_interval"], 0),
          receive_maximum: ConnectionHelpers.parse_int(connection_params["receive_maximum"], 0),
          # Preserve the existing connection_time if available
          connection_time: connection.connection_time,
          maximum_packet_size:
            ConnectionHelpers.parse_int(connection_params["maximum_packet_size"], 0),
          topic_alias_maximum:
            ConnectionHelpers.parse_int(connection_params["topic_alias_maximum"], 0),
          # 保留连接状态
          status: connection_params["status"] || "disconnected",
          request_response_info: connection_params["request_response_info"] == "on",
          request_problem_info: connection_params["request_problem_info"] == "on",
          user_properties: user_properties,
          will_topic: connection_params["will_topic"],
          will_qos: connection_params["will_qos"] || "0",
          will_retain: connection_params["will_retain"] == "on",
          will_payload: connection_params["will_payload"],
          will_payload_format: connection_params["will_payload_format"] == "on",
          will_delay_interval:
            ConnectionHelpers.parse_int(connection_params["will_delay_interval"], 0),
          will_message_expiry:
            ConnectionHelpers.parse_int(connection_params["will_message_expiry"], 0),
          will_content_type: connection_params["will_content_type"],
          will_response_topic: connection_params["will_response_topic"],
          will_correlation_data: connection_params["will_correlation_data"],
          # Preserve existing topics or initialize as empty list
          topics: Map.get(connection, :topics, []),
          # Preserve existing scheduled messages or initialize as empty list
          scheduled_messages: Map.get(connection, :scheduled_messages, [])
        }

        # Get existing connections
        connections = Map.get(connection_set, :connections, [])

        # Update the connection in the list
        updated_connections =
          Enum.map(connections, fn conn ->
            if conn.client_id == old_client_id, do: updated_connection, else: conn
          end)

        # Update the connection set with the updated connections
        updated_set = Map.put(connection_set, :connections, updated_connections)

        # Update the connection sets list
        connection_sets =
          Enum.map(socket.assigns.connection_sets, fn set ->
            if set.name == connection_set.name, do: updated_set, else: set
          end)

        # Save the updated connection sets to persistent storage
        MqttableWeb.ConnectionSets.Manager.save_connection_set(connection_sets)

        # If the connection was connected, handle reconnection
        if was_connected do
          handle_reconnection_after_update(
            old_client_id,
            client_id,
            updated_connection,
            connection_set
          )
        end

        # Reset the connection form to default
        default_connection = %{
          name: ConnectionHelpers.generate_random_connection_name(),
          client_id: "mqtt_" <> ConnectionHelpers.generate_random_string(8),
          username: "",
          password: "",
          mqtt_version: "5.0",
          connect_timeout: 45,
          keep_alive: 300,
          clean_start: true,
          session_expiry_interval: 0,
          receive_maximum: nil,
          maximum_packet_size: nil,
          topic_alias_maximum: nil,
          request_response_info: false,
          request_problem_info: false,
          user_properties: [%{key: "", value: ""}],
          will_topic: "",
          will_qos: "0",
          will_retain: false,
          will_payload: "",
          will_payload_format: false,
          will_delay_interval: 0,
          will_message_expiry: 0,
          will_content_type: "",
          will_response_topic: "",
          will_correlation_data: "",
          topics: [],
          connection_time: nil
        }

        # Close the modal and update the socket
        {:noreply,
         socket
         |> assign(:connection_sets, connection_sets)
         |> assign(:edit_connection, default_connection)
         |> assign(:show_modal, false)
         |> assign(:modal_type, nil)
         |> assign(:connection_set, nil)}
    end
  end

  @doc """
  Handles the delete_connection event.
  """
  def handle_delete_connection(socket, %{"set_name" => set_name, "client_id" => client_id}) do
    # Find the connection set by name
    set = ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, set_name)

    if set do
      # Disconnect the client if it's connected
      Mqttable.MqttClient.Manager.disconnect(client_id)

      # Get existing connections
      connections = Map.get(set, :connections, [])

      # Remove the connection with the given client_id
      updated_connections =
        Enum.reject(connections, fn conn ->
          conn.client_id == client_id
        end)

      # Update the set with the updated connections
      updated_set = Map.put(set, :connections, updated_connections)

      # Update the connection sets list
      connection_sets =
        Enum.map(socket.assigns.connection_sets, fn s ->
          if s.name == set_name, do: updated_set, else: s
        end)

      # Save the updated connection sets to persistent storage
      MqttableWeb.ConnectionSets.Manager.save_connection_set(connection_sets)

      # Close the modal and update the socket
      {:noreply,
       socket
       |> assign(:connection_sets, connection_sets)
       |> assign(:show_modal, false)
       |> assign(:modal_type, nil)
       |> assign(:edit_connection, nil)
       |> assign(:connection_set, nil)}
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handles the add_user_property event.
  """
  def handle_add_user_property(socket) do
    # Get the current user properties or initialize an empty list
    user_properties = Map.get(socket.assigns.edit_connection, :user_properties, [])

    # Add a new empty property to the list
    updated_properties = user_properties ++ [%{key: "", value: ""}]

    # Update the edit_connection in the socket
    edit_connection =
      Map.put(socket.assigns.edit_connection, :user_properties, updated_properties)

    # Save the updated connection immediately
    updated_socket = save_connection_immediately(socket, edit_connection)

    {:noreply, updated_socket}
  end

  @doc """
  Handles the remove_user_property event.
  """
  def handle_remove_user_property(socket, %{"index" => index}) do
    # Convert index to integer
    index = String.to_integer(index)

    # Remove the property at the given index
    user_properties =
      socket.assigns.edit_connection.user_properties
      |> Enum.with_index()
      |> Enum.filter(fn {_, i} -> i != index end)
      |> Enum.map(fn {prop, _} -> prop end)

    # Update the edit_connection in the socket
    edit_connection = Map.put(socket.assigns.edit_connection, :user_properties, user_properties)

    # Save the updated connection immediately
    updated_socket = save_connection_immediately(socket, edit_connection)

    {:noreply, updated_socket}
  end

  @doc """
  Handles the user_property_changed event for real-time updates.
  """
  def handle_user_property_changed(socket, params) do
    # Get current user properties
    current_properties = Map.get(socket.assigns.edit_connection, :user_properties, [])

    # Parse the user property fields from params and update current properties
    updated_properties = parse_user_properties_from_params(params, current_properties)

    # Update the edit_connection in the socket
    edit_connection =
      Map.put(socket.assigns.edit_connection, :user_properties, updated_properties)

    # Save the updated connection immediately
    updated_socket = save_connection_immediately(socket, edit_connection)

    {:noreply, updated_socket}
  end

  @doc """
  Handles the check_new_property event.
  This is a legacy function that is no longer used but kept for compatibility.
  """
  def handle_check_new_property(socket, %{"value" => _value}) do
    # Simply return the socket without changes
    {:noreply, socket}
  end

  @doc """
  Handles the mqtt_version_changed event.
  Updates the edit_connection with the selected MQTT version.
  """
  def handle_mqtt_version_changed(socket, %{"connection" => %{"mqtt_version" => mqtt_version}}) do
    # Update the edit_connection in the socket with the new MQTT version
    edit_connection = Map.put(socket.assigns.edit_connection, :mqtt_version, mqtt_version)

    {:noreply, assign(socket, :edit_connection, edit_connection)}
  end

  # Private helper functions

  # Generates a unique client ID that doesn't conflict with existing connections
  defp generate_unique_client_id(existing_connections) do
    generate_unique_value(
      existing_connections,
      fn -> "mqtt_" <> ConnectionHelpers.generate_random_string(8) end,
      fn conn, value -> conn.client_id == value end
    )
  end

  # Generates a unique connection name that doesn't conflict with existing connections
  defp generate_unique_connection_name(existing_connections) do
    generate_unique_value(
      existing_connections,
      fn -> ConnectionHelpers.generate_random_connection_name() end,
      fn conn, value -> conn.name == value end
    )
  end

  # Generic function to generate unique values
  defp generate_unique_value(existing_connections, generator_fn, conflict_fn, max_attempts \\ 10) do
    # Fallback to a new generation if all attempts failed
    Enum.reduce_while(1..max_attempts, nil, fn _attempt, _acc ->
      candidate = generator_fn.()

      if Enum.any?(existing_connections, fn conn -> conflict_fn.(conn, candidate) end) do
        {:cont, nil}
      else
        {:halt, candidate}
      end
    end) || generator_fn.()
  end

  # Handles reconnection after updating connection settings.
  # If the connection was previously connected, this function will:
  # 1. Disconnect the old connection
  # 2. Reconnect with the new settings
  defp handle_reconnection_after_update(
         old_client_id,
         new_client_id,
         updated_connection,
         connection_set
       ) do
    require Logger

    # If the client_id changed, we need to disconnect the old one first
    if old_client_id != new_client_id do
      Logger.info(
        "Client ID changed from #{old_client_id} to #{new_client_id}, disconnecting old connection"
      )

      Mqttable.MqttClient.Manager.disconnect(old_client_id)

      # Clean up UI state related to the old client_id
      clean_ui_state_for_client_id_change(connection_set.name, old_client_id, new_client_id)
    else
      Logger.info("Reconnecting client #{old_client_id} with updated settings")
      Mqttable.MqttClient.Manager.disconnect(old_client_id)
    end

    # Wait a brief moment for the disconnection to complete
    Process.sleep(100)

    # Attempt to reconnect with the new settings
    case Mqttable.MqttClient.Manager.connect(updated_connection, connection_set) do
      {:ok, _client_id} ->
        Logger.info("Successfully reconnected client #{new_client_id}")

      {:error, reason, error_message} ->
        Logger.error(
          "Failed to reconnect client #{new_client_id}: #{inspect(reason)} - #{error_message}"
        )
    end
  end

  # Clean up UI state when client_id changes
  defp clean_ui_state_for_client_id_change(broker_name, old_client_id, new_client_id) do
    # Get current send modal form state for this broker
    current_form_state = Mqttable.ConnectionSets.get_send_modal_form_state(broker_name)

    if current_form_state && current_form_state["client_id"] == old_client_id do
      # Update the client_id in the form state to the new one
      updated_form_state = Map.put(current_form_state, "client_id", new_client_id)
      Mqttable.ConnectionSets.update_send_modal_form_state(broker_name, updated_form_state)

      Logger.debug(
        "Updated send modal form state client_id from #{old_client_id} to #{new_client_id}"
      )
    end
  end

  # Helper functions for user properties

  # Parses user properties from form parameters for form submission (like update_connection).
  # Extracts user_property_key_X and user_property_value_X fields and converts to atom-key structs.
  defp parse_user_properties_from_form_params(params) do
    Logger.debug("Parsing user properties from params: #{inspect(params)}")

    # Find all user property key/value pairs
    user_property_fields =
      params
      |> Enum.filter(fn {key, _value} ->
        key_str = to_string(key)
        String.starts_with?(key_str, "user_property_")
      end)

    Logger.debug("Found user property fields: #{inspect(user_property_fields)}")

    key_value_pairs =
      user_property_fields
      |> Enum.reduce(%{}, fn {param_key, value}, acc ->
        case extract_index_and_field(to_string(param_key)) do
          {index, field} ->
            current_prop = Map.get(acc, index, %{})
            updated_prop = Map.put(current_prop, field, value)
            Map.put(acc, index, updated_prop)

          _ ->
            acc
        end
      end)
      |> Map.values()
      |> Enum.map(fn prop ->
        %{
          key: Map.get(prop, "key", ""),
          value: Map.get(prop, "value", "")
        }
      end)
      |> Enum.filter(fn prop -> prop.key != "" end)

    Logger.debug("Parsed user properties: #{inspect(key_value_pairs)}")
    key_value_pairs
  end

  # Parses user properties from form parameters and updates the current properties list.
  # Converts from string-key form format to atom-key struct format for storage.
  defp parse_user_properties_from_params(params, current_properties) do
    # Convert current properties from atom-key structs to string-key maps for processing
    current_properties_as_maps =
      Enum.map(current_properties, fn prop ->
        %{"key" => prop.key || "", "value" => prop.value || ""}
      end)

    # Extract user property fields from params and update current properties
    updated_properties_as_maps =
      params
      |> Enum.filter(fn {key, _value} ->
        key_str = to_string(key)
        String.starts_with?(key_str, "user_property_")
      end)
      |> Enum.reduce(current_properties_as_maps, fn {param_key, value}, acc ->
        case extract_index_and_field(to_string(param_key)) do
          {index, field} when index < length(acc) ->
            List.update_at(acc, index, fn property ->
              Map.put(property, field, value)
            end)

          _ ->
            acc
        end
      end)

    # Convert back to atom-key structs for storage
    Enum.map(updated_properties_as_maps, fn prop ->
      %{key: prop["key"] || "", value: prop["value"] || ""}
    end)
  end

  # Extracts index and field from parameter keys like "user_property_key_0" or "user_property_value_0".
  defp extract_index_and_field(param_key) do
    case String.split(param_key, "_") do
      ["user", "property", field, index_str] ->
        case Integer.parse(index_str) do
          {index, ""} -> {index, field}
          _ -> nil
        end

      _ ->
        nil
    end
  end

  # Saves the connection immediately to persistent storage.
  # This enables real-time persistence of user property changes.
  defp save_connection_immediately(socket, updated_connection) do
    connection_set = socket.assigns.connection_set

    if connection_set && updated_connection do
      # Find and update the connection in the connection set
      connections = Map.get(connection_set, :connections, [])

      updated_connections =
        case socket.assigns[:edit_connection_index] do
          nil ->
            # This is a new connection, add it to the list
            connections ++ [updated_connection]

          index when is_integer(index) ->
            # This is an existing connection, update it
            List.replace_at(connections, index, updated_connection)

          _ ->
            # Fallback: find by client_id and update
            Enum.map(connections, fn conn ->
              if conn.client_id == updated_connection.client_id do
                updated_connection
              else
                conn
              end
            end)
        end

      # Update the connection set
      updated_set = Map.put(connection_set, :connections, updated_connections)

      # Update the connection sets list
      connection_sets =
        Enum.map(socket.assigns.connection_sets, fn set ->
          if set.name == connection_set.name, do: updated_set, else: set
        end)

      # Save to persistent storage
      MqttableWeb.ConnectionSets.Manager.save_connection_set(connection_sets)

      # Update the socket with the new state
      socket
      |> assign(:connection_sets, connection_sets)
      |> assign(:connection_set, updated_set)
      |> assign(:edit_connection, updated_connection)
    else
      # If we don't have the necessary data, just update the edit_connection
      assign(socket, :edit_connection, updated_connection)
    end
  end
end
